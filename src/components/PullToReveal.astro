---
// Pull-to-Reveal Interactive Widget Component
// This component creates a hidden widget at the top of the page that can be revealed by pulling down
---

<!-- Pull hint indicator - positioned outside the hidden widget -->
<div
  class="pull-hint-container fixed top-0 left-0 right-0 z-[70] pointer-events-none"
>
  <div class="pull-hint" aria-hidden="true"></div>

  <!-- Initial hint that appears briefly -->
  <div
    class="initial-hint absolute top-4 left-1/2 transform -translate-x-1/2 text-xs text-black/50 dark:text-white/50 opacity-0 transition-opacity duration-1000"
    aria-hidden="true"
  >
    ↓ Pull down for a surprise ↓
  </div>
</div>

<div
  id="pull-to-reveal"
  class="pull-to-reveal"
  role="dialog"
  aria-hidden="true"
  aria-label="Hidden easter egg widget"
>
  <!-- Widget content -->
  <div class="relative h-32 flex items-center justify-center overflow-hidden">
    <!-- Background pattern -->
    <div class="absolute inset-0 opacity-10">
      <div
        class="absolute inset-0 bg-gradient-to-r from-purple-400 via-pink-400 to-red-400 animate-pulse"
      >
      </div>
      <div
        class="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.1)_1px,transparent_1px)] bg-[length:20px_20px]"
      >
      </div>
    </div>

    <!-- Easter egg content (initially hidden) -->
    <div
      class="easter-egg-content relative z-10 text-center"
      style="display: none;"
    >
      <!-- Main message -->
      <div class="space-y-2">
        <div class="text-4xl easter-emoji">🎉</div>
        <h3 class="text-lg font-bold text-black dark:text-white">
          You found the secret!
        </h3>
        <p class="text-sm text-black/70 dark:text-white/70 max-w-xs mx-auto">
          Thanks for exploring! I love building interactive experiences like
          this one.
        </p>

        <!-- Fun stats or personal touch -->
        <div
          class="mt-4 flex justify-center space-x-4 text-xs text-black/60 dark:text-white/60"
        >
          <div class="flex items-center space-x-1">
            <span>☕</span>
            <span>Coffee lover</span>
          </div>
          <div class="flex items-center space-x-1">
            <span>🚀</span>
            <span>Always learning</span>
          </div>
          <div class="flex items-center space-x-1">
            <span>💻</span>
            <span>Code enthusiast</span>
          </div>
        </div>

        <!-- Close hint -->
        <div class="mt-3 text-xs text-black/50 dark:text-white/50">
          Click anywhere or press ESC to close
        </div>
      </div>
    </div>

    <!-- Confetti container -->
    <div class="confetti-container absolute inset-0 pointer-events-none"></div>
  </div>
</div>

<!-- Temporary test button for debugging -->
<div class="fixed bottom-4 right-4 z-[80]">
  <button
    id="test-pull-widget"
    class="bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-blue-600 transition-colors"
    onclick="window.testPullWidget()"
  >
    Test Pull Widget
  </button>
</div>

<!-- Load the pull-to-reveal JavaScript -->
<script is:inline src="/js/pull-to-reveal.js"></script>

<script is:inline>
  // Global test function
  window.testPullWidget = function () {
    console.log("Test button clicked");
    if (window.pullToRevealInstance) {
      if (window.pullToRevealInstance.isActive) {
        window.pullToRevealInstance.closeWidget();
      } else {
        window.pullToRevealInstance.revealWidget();
      }
    } else {
      console.error("Pull-to-reveal instance not found");
    }
  };
</script>

<style>
  /* Additional component-specific styles */
  #pull-to-reveal {
    /* Ensure it's above everything else */
    z-index: 9999;
  }

  /* Smooth transitions for the widget */
  .pull-to-reveal {
    will-change: transform;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .easter-egg-content {
      padding: 0 1rem;
    }

    .easter-egg-content h3 {
      font-size: 1rem;
    }

    .easter-egg-content p {
      font-size: 0.75rem;
    }

    .easter-egg-content .text-xs {
      font-size: 0.625rem;
    }
  }

  /* Accessibility improvements */
  @media (prefers-reduced-motion: reduce) {
    .pull-to-reveal,
    .pull-hint,
    .easter-egg-content,
    .confetti-piece {
      animation: none !important;
      transition: none !important;
    }

    .wiggle {
      animation: none !important;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .pull-to-reveal {
      border-width: 2px;
      background: white;
    }

    .dark .pull-to-reveal {
      background: black;
      border-color: white;
    }

    .pull-hint {
      background: black !important;
    }

    .dark .pull-hint {
      background: white !important;
    }
  }
</style>
