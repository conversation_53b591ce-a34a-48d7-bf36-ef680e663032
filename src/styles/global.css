@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --copy-btn-margin: 10px;
}

@layer base {
  @font-face {
    font-family: "Atkinson";
    src: url("/fonts/atkinson-regular.woff") format("woff");
    font-weight: 400;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: "Atkinson";
    src: url("/fonts/atkinson-bold.woff") format("woff");
    font-weight: 700;
    font-style: normal;
    font-display: swap;
  }
}

html {
  overflow-y: scroll;
  color-scheme: light;
  background-color: white;
  font-family: "Atkinson", sans-serif;
}

html.dark {
  color-scheme: dark;
  background-color: black;
}

html,
body {
  @apply h-full w-full antialiased;
  @apply bg-white dark:bg-black;
  @apply text-black/75 dark:text-white/75;
}

/* Custom cursor styling for desktop devices */
@media (min-width: 769px) {
  html {
    cursor: none;
  }

  a,
  button,
  input[type="button"],
  input[type="submit"],
  [role="button"],
  .cursor-pointer {
    cursor: none;
  }
}

body {
  @apply relative flex flex-col;
}

main {
  @apply flex flex-col flex-1 bg-white dark:bg-black;
}

header {
  @apply border-b;
  @apply transition-all duration-300 ease-in-out;
}

header:not(.scrolled) {
  @apply bg-transparent border-transparent;
}

header.scrolled {
  @apply bg-white/75 dark:bg-black/50;
  @apply border-black/10 dark:border-white/25;
  @apply backdrop-blur-sm saturate-200;
}

article {
  @apply prose dark:prose-invert max-w-full;
}

.page-heading {
  @apply font-semibold text-black dark:text-white;
}

.blend {
  @apply transition-all duration-300 ease-in-out;
}

/** Light theme particles on home page */
@keyframes animateParticle {
  from {
    transform: translateY(0px);
  }
  to {
    transform: translateY(-2000px);
  }
}

/** styles for public /animation.js */
.animate {
  opacity: 0;
  transform: translateY(50px);
  transition:
    opacity 1s ease,
    transform 1s ease;
}

.animate.show {
  opacity: 1;
  transform: translateY(0);
}

article img {
  padding-top: 20px;
  padding-bottom: 20px;
  display: block;
  margin: 0 auto;
}

/**
 * TWINKLE STARS
 */

#twinkle-star.template {
  @apply absolute -left-full; /* hide offscreen */
}

#twinkle-star.twinkle {
  @apply animate-twinkle; /* defined in tailwind.config */
}

/**
 * Meteors
 */

#meteors .shower {
  @apply absolute inset-0 top-0;
  @apply left-1/2 -translate-x-1/2;
  @apply w-screen aspect-square;
}

#meteors .meteor {
  @apply animate-meteor; /* defined in tailwind.config */
  @apply absolute top-1/2 left-1/2 w-px h-[75vh];
  @apply bg-gradient-to-b from-white to-transparent;
}

#meteors .shower.ur {
  @apply rotate-45;
}

#meteors .shower.dr {
  @apply rotate-135;
}

#meteors .shower.dl {
  @apply rotate-225;
}

#meteors .shower.ul {
  @apply rotate-315;
}

.copy-cnt {
  @apply absolute w-full;
  top: var(--copy-btn-margin);
}
.copy-btn {
  @apply w-[30px] fixed;
  left: calc(100% - var(--copy-btn-margin));
  transform: translateX(-100%);
}

.copy-svg {
  @apply w-full aspect-square text-white opacity-70 hover:opacity-90;
}

.navbar-brand {
  border: 1px solid #ff6601;
  border-right: 2px dotted #ff6601;
  padding: 1px 3px;
  border-radius: 60% 40% 40% 20% / 70% 50% 30% 25%;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.6;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/**
 * PULL TO REVEAL WIDGET
 */

.pull-to-reveal {
  @apply fixed top-0 left-0 right-0 z-[60];
  @apply bg-gradient-to-b from-white/95 to-white/80 dark:from-black/95 dark:to-black/80;
  @apply backdrop-blur-md border-b border-black/10 dark:border-white/10;
  transform: translateY(-100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  touch-action: none; /* Prevent default touch behaviors */
}

.pull-to-reveal.pulling {
  transition: none;
}

.pull-to-reveal.revealed {
  transform: translateY(0);
}

.pull-hint {
  @apply absolute top-0 left-1/2 transform -translate-x-1/2;
  @apply w-8 h-1 bg-black/50 dark:bg-white/50 rounded-full;
  @apply transition-all duration-300;
  top: 8px;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.2);
  opacity: 1; /* Make it visible by default */
}

.pull-hint.active {
  @apply bg-black/40 dark:bg-white/40;
  @apply animate-bounce;
}

@keyframes confetti-fall {
  0% {
    transform: translateY(-100vh) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotate(720deg);
    opacity: 0;
  }
}

@keyframes bounce-in {
  0% {
    transform: scale(0) rotate(-180deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.2) rotate(-90deg);
    opacity: 1;
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

@keyframes wiggle {
  0%,
  100% {
    transform: rotate(-3deg);
  }
  50% {
    transform: rotate(3deg);
  }
}

.confetti-piece {
  @apply absolute w-2 h-2 opacity-80;
  animation: confetti-fall 3s linear infinite;
}

.confetti-piece:nth-child(odd) {
  @apply bg-gradient-to-br from-purple-400 to-pink-400;
  animation-delay: 0s;
}

.confetti-piece:nth-child(even) {
  @apply bg-gradient-to-br from-blue-400 to-cyan-400;
  animation-delay: 0.5s;
}

.confetti-piece:nth-child(3n) {
  @apply bg-gradient-to-br from-yellow-400 to-orange-400;
  animation-delay: 1s;
}

.confetti-piece:nth-child(4n) {
  @apply bg-gradient-to-br from-green-400 to-emerald-400;
  animation-delay: 1.5s;
}

.easter-egg-content {
  animation: bounce-in 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.wiggle {
  animation: wiggle 0.5s ease-in-out infinite;
}
