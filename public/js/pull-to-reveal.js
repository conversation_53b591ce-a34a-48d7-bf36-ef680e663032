class PullToReveal {
  constructor() {
    this.widget = null;
    this.hint = null;
    this.content = null;
    this.confettiContainer = null;

    this.isActive = false;
    this.isDragging = false;
    this.startY = 0;
    this.currentY = 0;
    this.pullDistance = 0;
    this.threshold = 80; // Minimum pull distance to trigger
    this.maxPull = 150; // Maximum pull distance

    this.init();
  }

  init() {
    this.widget = document.getElementById("pull-to-reveal");
    if (!this.widget) {
      console.warn("Pull-to-reveal widget not found");
      return;
    }

    this.hint = document.querySelector(".pull-hint");
    this.initialHint = document.querySelector(".initial-hint");
    this.content = this.widget.querySelector(".easter-egg-content");
    this.confettiContainer = this.widget.querySelector(".confetti-container");

    console.log("Pull-to-reveal widget initialized successfully");
    console.log("Hint element:", this.hint);
    console.log("Initial hint element:", this.initialHint);

    this.setupEventListeners();
    this.showInitialHint();
    this.showHintPeriodically();

    // Debug: Add a temporary test to show the widget after 5 seconds
    setTimeout(() => {
      console.log("Debug: Temporarily showing widget for testing");
      this.revealWidget();
      setTimeout(() => {
        this.closeWidget();
      }, 3000);
    }, 5000);
  }

  setupEventListeners() {
    // Touch events for mobile
    document.addEventListener("touchstart", this.handleTouchStart.bind(this), {
      passive: false,
    });
    document.addEventListener("touchmove", this.handleTouchMove.bind(this), {
      passive: false,
    });
    document.addEventListener("touchend", this.handleTouchEnd.bind(this), {
      passive: false,
    });

    // Mouse events for desktop
    document.addEventListener("mousedown", this.handleMouseDown.bind(this));
    document.addEventListener("mousemove", this.handleMouseMove.bind(this));
    document.addEventListener("mouseup", this.handleMouseEnd.bind(this));

    // Close widget when clicking outside or pressing escape
    document.addEventListener("click", this.handleOutsideClick.bind(this));
    document.addEventListener("keydown", this.handleKeyDown.bind(this));

    // Prevent scroll when widget is revealed
    document.addEventListener("scroll", this.handleScroll.bind(this));
  }

  handleTouchStart(e) {
    if (this.isActive) return;

    const touch = e.touches[0];
    if (touch.clientY <= 50) {
      // Only trigger near top of screen
      this.startPull(touch.clientY);
      e.preventDefault();
    }
  }

  handleTouchMove(e) {
    if (!this.isDragging) return;

    const touch = e.touches[0];
    this.updatePull(touch.clientY);
    e.preventDefault();
  }

  handleTouchEnd(e) {
    if (!this.isDragging) return;

    this.endPull();
    e.preventDefault();
  }

  handleMouseDown(e) {
    if (this.isActive || e.clientY > 50) return;

    this.startPull(e.clientY);
  }

  handleMouseMove(e) {
    if (!this.isDragging) return;

    this.updatePull(e.clientY);
  }

  handleMouseEnd(e) {
    if (!this.isDragging) return;

    this.endPull();
  }

  startPull(y) {
    this.isDragging = true;
    this.startY = y;
    this.currentY = y;
    this.widget.classList.add("pulling");
    this.hint.classList.add("active");

    document.body.style.userSelect = "none";
  }

  updatePull(y) {
    this.currentY = y;
    this.pullDistance = Math.max(
      0,
      Math.min(this.currentY - this.startY, this.maxPull)
    );

    const progress = this.pullDistance / this.threshold;
    const translateY = -100 + progress * 100;

    this.widget.style.transform = `translateY(${Math.max(-100, translateY)}%)`;

    // Update hint opacity based on progress
    if (this.hint) {
      this.hint.style.opacity = Math.min(1, progress * 2);
    }
  }

  endPull() {
    this.isDragging = false;
    this.widget.classList.remove("pulling");
    this.hint.classList.remove("active");

    document.body.style.userSelect = "";

    if (this.pullDistance >= this.threshold) {
      this.revealWidget();
    } else {
      this.hideWidget();
    }

    this.pullDistance = 0;
  }

  revealWidget() {
    this.isActive = true;
    this.widget.classList.add("revealed");
    this.widget.style.transform = "";
    this.widget.setAttribute("aria-hidden", "false");

    // Trigger easter egg animation
    setTimeout(() => {
      this.triggerEasterEgg();
    }, 300);
  }

  hideWidget() {
    this.widget.style.transform = "";
    setTimeout(() => {
      this.widget.classList.remove("revealed");
    }, 300);
  }

  triggerEasterEgg() {
    if (this.content) {
      this.content.style.display = "block";
    }

    this.createConfetti();

    // Add wiggle effect to emoji
    const emoji = this.widget.querySelector(".easter-emoji");
    if (emoji) {
      emoji.classList.add("wiggle");
      setTimeout(() => {
        emoji.classList.remove("wiggle");
      }, 2000);
    }
  }

  createConfetti() {
    if (!this.confettiContainer) return;

    // Clear existing confetti
    this.confettiContainer.innerHTML = "";

    // Create confetti pieces
    for (let i = 0; i < 50; i++) {
      const piece = document.createElement("div");
      piece.className = "confetti-piece";
      piece.style.left = Math.random() * 100 + "%";
      piece.style.animationDelay = Math.random() * 3 + "s";
      piece.style.animationDuration = Math.random() * 2 + 2 + "s";

      this.confettiContainer.appendChild(piece);
    }

    // Remove confetti after animation
    setTimeout(() => {
      this.confettiContainer.innerHTML = "";
    }, 5000);
  }

  closeWidget() {
    this.isActive = false;
    this.widget.classList.remove("revealed");
    this.widget.setAttribute("aria-hidden", "true");

    if (this.content) {
      this.content.style.display = "none";
    }

    if (this.confettiContainer) {
      this.confettiContainer.innerHTML = "";
    }
  }

  handleOutsideClick(e) {
    if (this.isActive && !this.widget.contains(e.target)) {
      this.closeWidget();
    }
  }

  handleKeyDown(e) {
    if (e.key === "Escape" && this.isActive) {
      this.closeWidget();
    }

    // Debug: Press 'P' to test the pull-to-reveal widget
    if (e.key === "p" || e.key === "P") {
      console.log("Debug: Manual widget test triggered");
      if (this.isActive) {
        this.closeWidget();
      } else {
        this.revealWidget();
      }
    }
  }

  handleScroll(e) {
    if (this.isActive && window.scrollY > 0) {
      // Prevent scrolling when widget is active
      window.scrollTo(0, 0);
    }
  }

  showInitialHint() {
    // Show initial hint after page load
    setTimeout(() => {
      if (this.initialHint && !this.isActive) {
        this.initialHint.style.opacity = "1";
        setTimeout(() => {
          this.initialHint.style.opacity = "0";
        }, 3000);
      }
    }, 2000);
  }

  showHintPeriodically() {
    // Show hint every 15 seconds for first-time users
    setInterval(() => {
      if (!this.isActive && !this.isDragging) {
        this.hint.classList.add("active");
        setTimeout(() => {
          this.hint.classList.remove("active");
        }, 1000);
      }
    }, 15000);
  }
}

// Global instance to prevent multiple initializations
let pullToRevealInstance = null;

// Initialize when DOM is loaded
function initializePullToReveal() {
  if (pullToRevealInstance) {
    return; // Already initialized
  }
  pullToRevealInstance = new PullToReveal();
  // Expose globally for testing
  window.pullToRevealInstance = pullToRevealInstance;
}

document.addEventListener("DOMContentLoaded", initializePullToReveal);

// Re-initialize on Astro page transitions
document.addEventListener("astro:after-swap", () => {
  pullToRevealInstance = null; // Reset instance
  initializePullToReveal();
});
